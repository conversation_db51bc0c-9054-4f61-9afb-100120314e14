# 🎬 HTML到PowerPoint转换器

## 📋 项目简介

这是一个专门用于将HTML培训教程文件转换为PowerPoint演示文稿的Python工具。特别针对短视频创作教程进行了优化，能够保持原有的内容层次结构和格式特色。

## ✨ 主要特性

- 🎯 **智能内容解析**: 自动识别HTML中的标题、段落、列表和卡片内容
- 🎨 **美观的PPT设计**: 基于原HTML配色方案，创建专业的演示文稿
- 📊 **流程图支持**: 自动将HTML中的流程图转换为PPT流程图
- 📑 **自动分页**: 内容过多时自动分成多个幻灯片
- 🎭 **多种布局**: 支持标题页、内容页、流程图页等多种布局

## 🛠️ 系统要求

- Python 3.6 或更高版本
- Windows/macOS/Linux 操作系统

## 📦 安装步骤

### 方法一：自动安装（推荐）

1. **运行依赖安装脚本**
   ```bash
   python install_dependencies.py
   ```

### 方法二：手动安装

1. **安装必需的Python库**
   ```bash
   pip install beautifulsoup4 python-pptx lxml
   ```

## 🚀 使用方法

### 基本使用

1. **确保HTML文件存在**
   - 将要转换的HTML文件命名为 `短视频创作教程.html`
   - 放在与转换器相同的目录下

2. **运行转换器**
   ```bash
   python html_to_pptx_converter.py
   ```

3. **查看结果**
   - 转换完成后会生成 `短视频创作教程.pptx` 文件
   - 使用Microsoft PowerPoint或其他兼容软件打开

### 高级使用

如果需要转换其他HTML文件，可以修改转换器代码中的文件路径：

```python
# 在 main() 函数中修改这两行
html_file = "你的HTML文件名.html"
output_file = "输出的PPT文件名.pptx"
```

## 📊 转换效果

### 输入内容类型
- ✅ HTML标题 (h1, h2, h3)
- ✅ 段落文本 (p)
- ✅ 列表内容 (ul, li)
- ✅ 卡片布局 (.card)
- ✅ 流程图 (.flow-chart)
- ✅ 技巧列表 (.tips-list)

### 输出PPT结构
1. **标题幻灯片** - 主标题和副标题
2. **目录幻灯片** - 课程大纲
3. **内容幻灯片** - 各章节详细内容
4. **流程图幻灯片** - 可视化流程展示
5. **结束幻灯片** - 感谢页面

## 🎨 设计特色

### 配色方案
- **主色调**: 蓝紫渐变 (#667eea → #764ba2)
- **文本色**: 深灰色 (#333333)
- **强调色**: 橙色系 (#fcb69f)

### 字体设置
- **标题**: 32-44pt，加粗
- **正文**: 18pt，常规
- **流程图**: 14pt，加粗白字

## 🔧 自定义选项

### 修改颜色主题
在 `HTMLToPPTXConverter` 类的 `__init__` 方法中修改 `self.colors` 字典：

```python
self.colors = {
    'primary': RGBColor(102, 126, 234),    # 主色
    'secondary': RGBColor(118, 75, 162),   # 辅色
    'text': RGBColor(51, 51, 51),          # 文本色
    # ... 其他颜色
}
```

### 调整内容分页
修改 `max_items_per_slide` 变量来控制每页显示的内容数量：

```python
max_items_per_slide = 6  # 每页最多6个内容项
```

## 📝 使用示例

### 转换成功示例
```
🎬 HTML到PowerPoint转换器
==================================================
📦 正在处理: 短视频创作教程.html
✅ 转换完成！PPT文件已保存至: 短视频创作教程.pptx
📊 共生成 15 张幻灯片

🎯 转换建议：
1. 打开生成的PPT文件检查内容
2. 根据需要调整字体大小和布局
3. 添加更多视觉元素（图片、图表等）
4. 检查并优化幻灯片过渡效果
```

## ⚠️ 注意事项

1. **文件编码**: 确保HTML文件使用UTF-8编码
2. **文件路径**: HTML文件应与转换器在同一目录
3. **内容复杂度**: 过于复杂的CSS样式可能无法完全保留
4. **图片处理**: 当前版本不支持图片转换，需要手动添加

## 🐛 常见问题

### Q: 转换后PPT打不开？
A: 检查是否安装了Microsoft PowerPoint或兼容软件（如WPS Office）

### Q: 中文显示乱码？
A: 确保HTML文件使用UTF-8编码保存

### Q: 某些内容没有转换？
A: 检查HTML结构是否符合预期，可能需要调整解析规则

### Q: 想要修改PPT样式？
A: 可以先转换，然后在PowerPoint中手动调整样式和布局

## 🔄 版本更新

### v1.0.0 (当前版本)
- ✅ 基础HTML到PPT转换功能
- ✅ 支持多种内容类型解析
- ✅ 流程图自动生成
- ✅ 美观的设计模板

### 计划中的功能
- 🔲 图片转换支持
- 🔲 表格内容处理
- 🔲 更多PPT模板选择
- 🔲 批量转换功能

## 📞 技术支持

如果在使用过程中遇到问题，请：

1. 检查本文档的常见问题部分
2. 确认所有依赖库已正确安装
3. 验证HTML文件格式和编码
4. 查看控制台错误信息

## 📄 许可证

本项目采用MIT许可证，可自由使用和修改。

---

**🎯 提示**: 转换完成后，建议在PowerPoint中进一步优化演示文稿，添加动画效果和视觉元素，以获得最佳的演示效果。
