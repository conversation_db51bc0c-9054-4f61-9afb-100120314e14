#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖安装脚本
自动安装HTML到PPT转换所需的Python库
"""

import subprocess
import sys
import os

def install_package(package_name):
    """安装Python包"""
    try:
        print(f"📦 正在安装 {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    """主函数"""
    print("🔧 HTML到PPT转换器 - 依赖安装")
    print("=" * 50)
    
    # 需要安装的包列表
    required_packages = [
        ("beautifulsoup4", "bs4"),  # (pip包名, import名)
        ("python-pptx", "pptx"),
        ("lxml", "lxml")
    ]
    
    installed_count = 0
    failed_count = 0
    
    for pip_name, import_name in required_packages:
        if check_package(import_name):
            print(f"✅ {pip_name} 已安装")
            installed_count += 1
        else:
            if install_package(pip_name):
                installed_count += 1
            else:
                failed_count += 1
    
    print("\n" + "=" * 50)
    print(f"📊 安装结果统计:")
    print(f"✅ 成功安装/已存在: {installed_count} 个包")
    print(f"❌ 安装失败: {failed_count} 个包")
    
    if failed_count == 0:
        print("\n🎉 所有依赖安装完成！现在可以运行转换器了。")
        print("💡 使用方法: python html_to_pptx_converter.py")
    else:
        print("\n⚠️  部分依赖安装失败，请手动安装或检查网络连接。")
        print("💡 手动安装命令:")
        for pip_name, _ in required_packages:
            print(f"   pip install {pip_name}")

if __name__ == "__main__":
    main()
