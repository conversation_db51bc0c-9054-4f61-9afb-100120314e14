@echo off
chcp 65001 >nul
title HTML到PPT转换器

echo.
echo ========================================
echo 🎬 HTML到PowerPoint转换器
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未检测到Python环境
    echo 💡 请先安装Python 3.6或更高版本
    echo 📥 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检测正常
echo.

:: 检查HTML文件是否存在
if not exist "短视频创作教程.html" (
    echo ❌ 错误：找不到 "短视频创作教程.html" 文件
    echo 💡 请确保HTML文件与此脚本在同一目录下
    pause
    exit /b 1
)

echo ✅ 找到HTML源文件
echo.

:: 安装依赖
echo 📦 正在检查并安装必要的依赖库...
python install_dependencies.py
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo.
echo 🚀 开始转换HTML到PowerPoint...
echo.

:: 执行转换
python html_to_pptx_converter.py
if errorlevel 1 (
    echo ❌ 转换失败
    pause
    exit /b 1
)

echo.
echo 🎉 转换完成！
echo.

:: 询问是否打开PPT文件
set /p choice="是否立即打开生成的PPT文件？(Y/N): "
if /i "%choice%"=="Y" (
    if exist "短视频创作教程.pptx" (
        echo 📂 正在打开PPT文件...
        start "" "短视频创作教程.pptx"
    ) else (
        echo ❌ 找不到生成的PPT文件
    )
)

echo.
echo 📋 文件位置：
echo    源文件：短视频创作教程.html
echo    输出文件：短视频创作教程.pptx
echo.
echo 💡 提示：您可以在PowerPoint中进一步编辑和美化演示文稿
echo.

pause
