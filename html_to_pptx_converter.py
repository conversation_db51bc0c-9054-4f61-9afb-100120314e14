#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML到PowerPoint转换器
专门用于将短视频创作教程HTML文件转换为PPT演示文稿
"""

import os
import re
from bs4 import BeautifulSoup
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE

class HTMLToPPTXConverter:
    def __init__(self):
        self.prs = Presentation()
        self.slide_width = Inches(10)
        self.slide_height = Inches(7.5)
        
        # 定义颜色主题（基于原HTML的配色）
        self.colors = {
            'primary': RGBColor(102, 126, 234),    # #667eea
            'secondary': RGBColor(118, 75, 162),   # #764ba2
            'text': RGBColor(51, 51, 51),          # #333
            'light_text': RGBColor(102, 102, 102), # #666
            'white': RGBColor(255, 255, 255),
            'accent': RGBColor(252, 182, 159)      # #fcb69f
        }
    
    def create_title_slide(self, title, subtitle=""):
        """创建标题幻灯片"""
        slide_layout = self.prs.slide_layouts[0]  # 标题布局
        slide = self.prs.slides.add_slide(slide_layout)
        
        # 设置标题
        title_shape = slide.shapes.title
        title_shape.text = title
        title_paragraph = title_shape.text_frame.paragraphs[0]
        title_paragraph.font.size = Pt(44)
        title_paragraph.font.color.rgb = self.colors['primary']
        title_paragraph.font.bold = True
        title_paragraph.alignment = PP_ALIGN.CENTER
        
        # 设置副标题
        if subtitle and slide.placeholders[1]:
            subtitle_shape = slide.placeholders[1]
            subtitle_shape.text = subtitle
            subtitle_paragraph = subtitle_shape.text_frame.paragraphs[0]
            subtitle_paragraph.font.size = Pt(24)
            subtitle_paragraph.font.color.rgb = self.colors['secondary']
            subtitle_paragraph.alignment = PP_ALIGN.CENTER
        
        return slide
    
    def create_content_slide(self, title, content_items, slide_type="bullet"):
        """创建内容幻灯片"""
        slide_layout = self.prs.slide_layouts[1]  # 标题和内容布局
        slide = self.prs.slides.add_slide(slide_layout)
        
        # 设置标题
        title_shape = slide.shapes.title
        title_shape.text = title
        title_paragraph = title_shape.text_frame.paragraphs[0]
        title_paragraph.font.size = Pt(32)
        title_paragraph.font.color.rgb = self.colors['primary']
        title_paragraph.font.bold = True
        
        # 设置内容
        content_shape = slide.placeholders[1]
        text_frame = content_shape.text_frame
        text_frame.clear()
        
        for i, item in enumerate(content_items):
            if i == 0:
                p = text_frame.paragraphs[0]
            else:
                p = text_frame.add_paragraph()
            
            # 处理不同类型的内容
            if isinstance(item, dict):
                # 如果是字典，包含标题和内容
                p.text = f"{item.get('title', '')}: {item.get('content', '')}"
                p.font.size = Pt(18)
                if item.get('bold', False):
                    p.font.bold = True
            else:
                # 普通文本
                p.text = str(item)
                p.font.size = Pt(18)
            
            p.font.color.rgb = self.colors['text']
            p.level = 0
        
        return slide
    
    def create_flow_chart_slide(self, title, steps):
        """创建流程图幻灯片"""
        slide_layout = self.prs.slide_layouts[6]  # 空白布局
        slide = self.prs.slides.add_slide(slide_layout)
        
        # 添加标题
        title_shape = slide.shapes.add_textbox(Inches(0.5), Inches(0.5), Inches(9), Inches(1))
        title_frame = title_shape.text_frame
        title_frame.text = title
        title_paragraph = title_frame.paragraphs[0]
        title_paragraph.font.size = Pt(32)
        title_paragraph.font.color.rgb = self.colors['primary']
        title_paragraph.font.bold = True
        title_paragraph.alignment = PP_ALIGN.CENTER
        
        # 创建流程步骤
        step_width = Inches(1.8)
        step_height = Inches(1.2)
        start_x = Inches(0.5)
        y_position = Inches(2.5)
        
        for i, step in enumerate(steps):
            x_position = start_x + i * Inches(2.2)
            
            # 创建圆角矩形
            shape = slide.shapes.add_shape(
                MSO_SHAPE.ROUNDED_RECTANGLE,
                x_position, y_position, step_width, step_height
            )
            
            # 设置形状样式
            shape.fill.solid()
            shape.fill.fore_color.rgb = self.colors['primary']
            shape.line.color.rgb = self.colors['secondary']
            shape.line.width = Pt(2)
            
            # 添加文本
            text_frame = shape.text_frame
            text_frame.text = step
            text_frame.margin_left = Inches(0.1)
            text_frame.margin_right = Inches(0.1)
            text_frame.margin_top = Inches(0.1)
            text_frame.margin_bottom = Inches(0.1)
            text_frame.vertical_anchor = MSO_ANCHOR.MIDDLE
            
            paragraph = text_frame.paragraphs[0]
            paragraph.font.size = Pt(14)
            paragraph.font.color.rgb = self.colors['white']
            paragraph.font.bold = True
            paragraph.alignment = PP_ALIGN.CENTER
            
            # 添加箭头（除了最后一个步骤）
            if i < len(steps) - 1:
                arrow_x = x_position + step_width + Inches(0.1)
                arrow_y = y_position + step_height / 2
                arrow = slide.shapes.add_shape(
                    MSO_SHAPE.RIGHT_ARROW,
                    arrow_x, arrow_y - Inches(0.2), Inches(0.4), Inches(0.4)
                )
                arrow.fill.solid()
                arrow.fill.fore_color.rgb = self.colors['secondary']
                arrow.line.fill.background()
        
        return slide
    
    def parse_html_content(self, html_content):
        """解析HTML内容"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 提取主标题
        main_title = soup.find('h1', class_='main-title')
        title_text = main_title.get_text(strip=True) if main_title else "短视频创作完整教程"
        
        # 提取副标题
        subtitle_elem = soup.find('p', style=lambda x: x and 'color: white' in x)
        subtitle_text = subtitle_elem.get_text(strip=True) if subtitle_elem else ""
        
        # 提取所有章节
        sections = soup.find_all('section', class_='section')
        
        return title_text, subtitle_text, sections
    
    def process_section(self, section):
        """处理单个章节"""
        # 获取章节标题
        title_elem = section.find('h2', class_='section-title')
        title = title_elem.get_text(strip=True) if title_elem else "未知章节"
        
        # 获取章节内容
        content_items = []
        
        # 处理段落
        paragraphs = section.find_all('p', recursive=False)
        for p in paragraphs:
            text = p.get_text(strip=True)
            if text:
                content_items.append(text)
        
        # 处理卡片内容
        cards = section.find_all('div', class_='card')
        for card in cards:
            card_title = card.find('h3', class_='card-title')
            card_content = card.find('div', class_='card-content')
            
            if card_title and card_content:
                title_text = card_title.get_text(strip=True)
                content_text = card_content.get_text(strip=True)
                content_items.append({
                    'title': title_text,
                    'content': content_text,
                    'bold': True
                })
        
        # 处理技巧列表
        tips_list = section.find('ul', class_='tips-list')
        if tips_list:
            tips = tips_list.find_all('li')
            for tip in tips:
                tip_text = tip.get_text(strip=True)
                if tip_text:
                    content_items.append(tip_text)
        
        # 处理流程图
        flow_chart = section.find('div', class_='flow-chart')
        flow_steps = []
        if flow_chart:
            steps = flow_chart.find_all('div', class_='flow-step')
            flow_steps = [step.get_text(strip=True) for step in steps]
        
        return title, content_items, flow_steps
    
    def convert_html_to_pptx(self, html_file_path, output_path):
        """主转换函数"""
        try:
            print(f"📖 正在读取HTML文件: {html_file_path}")

            # 读取HTML文件
            with open(html_file_path, 'r', encoding='utf-8') as file:
                html_content = file.read()

            print("🔍 正在解析HTML内容...")

            # 解析HTML内容
            title_text, subtitle_text, sections = self.parse_html_content(html_content)

            print(f"📋 发现 {len(sections)} 个章节")

            # 创建标题幻灯片
            print("🎨 创建标题幻灯片...")
            self.create_title_slide(title_text, subtitle_text)

            # 创建目录幻灯片
            toc_items = []
            for section in sections:
                title_elem = section.find('h2', class_='section-title')
                if title_elem:
                    toc_items.append(title_elem.get_text(strip=True))

            if toc_items:
                print("📑 创建目录幻灯片...")
                self.create_content_slide("📋 课程大纲", toc_items)

            # 处理每个章节
            for i, section in enumerate(sections, 1):
                print(f"⚙️  处理第 {i}/{len(sections)} 个章节...")
                title, content_items, flow_steps = self.process_section(section)

                # 如果有流程图，创建流程图幻灯片
                if flow_steps:
                    print(f"   📊 创建流程图: {title}")
                    self.create_flow_chart_slide(title, flow_steps)

                # 创建内容幻灯片
                if content_items:
                    # 如果内容太多，分成多个幻灯片
                    max_items_per_slide = 6

                    for j in range(0, len(content_items), max_items_per_slide):
                        slide_items = content_items[j:j + max_items_per_slide]
                        slide_title = title if j == 0 else f"{title} (续{j//max_items_per_slide + 1})"
                        print(f"   📄 创建内容幻灯片: {slide_title}")
                        self.create_content_slide(slide_title, slide_items)

            # 创建结束幻灯片
            print("🎊 创建结束幻灯片...")
            self.create_title_slide("🎉 感谢观看", "短视频创作，从这里开始！")

            # 保存PPT文件
            print(f"💾 保存PPT文件: {output_path}")
            self.prs.save(output_path)

            print("\n" + "="*50)
            print(f"✅ 转换完成！PPT文件已保存至: {output_path}")
            print(f"📊 共生成 {len(self.prs.slides)} 张幻灯片")
            print(f"📁 文件大小: {self.get_file_size(output_path)}")

            return True

        except FileNotFoundError:
            print(f"❌ 错误：找不到文件 {html_file_path}")
            print("💡 请确保HTML文件存在且路径正确")
            return False
        except PermissionError:
            print(f"❌ 错误：没有权限访问文件 {output_path}")
            print("💡 请检查文件是否被其他程序占用")
            return False
        except UnicodeDecodeError:
            print(f"❌ 错误：文件编码问题")
            print("💡 请确保HTML文件使用UTF-8编码")
            return False
        except Exception as e:
            print(f"❌ 转换过程中出现未知错误: {str(e)}")
            print("💡 请检查HTML文件格式是否正确")
            return False

    def get_file_size(self, file_path):
        """获取文件大小"""
        try:
            size = os.path.getsize(file_path)
            if size < 1024:
                return f"{size} B"
            elif size < 1024 * 1024:
                return f"{size / 1024:.1f} KB"
            else:
                return f"{size / (1024 * 1024):.1f} MB"
        except:
            return "未知"

def main():
    """主函数"""
    print("🎬 HTML到PowerPoint转换器")
    print("=" * 50)

    # 设置文件路径
    html_file = "短视频创作教程.html"
    output_file = "短视频创作教程.pptx"

    # 检查输入文件是否存在
    if not os.path.exists(html_file):
        print(f"❌ 错误：找不到文件 {html_file}")
        print("💡 请确保HTML文件与转换器在同一目录下")
        return

    # 显示文件信息
    file_size = os.path.getsize(html_file)
    print(f"📄 源文件: {html_file}")
    print(f"📊 文件大小: {file_size / 1024:.1f} KB")

    # 创建转换器实例
    converter = HTMLToPPTXConverter()

    # 执行转换
    print("\n🚀 开始转换...")
    success = converter.convert_html_to_pptx(html_file, output_file)

    if success:
        print("\n🎯 转换建议：")
        print("1. 打开生成的PPT文件检查内容")
        print("2. 根据需要调整字体大小和布局")
        print("3. 添加更多视觉元素（图片、图表等）")
        print("4. 检查并优化幻灯片过渡效果")
        print("5. 可以添加动画和过渡效果增强演示效果")

        print("\n📋 转换统计：")
        print(f"   ✅ 源文件: {html_file}")
        print(f"   ✅ 输出文件: {output_file}")
        print(f"   ✅ 幻灯片数量: {len(converter.prs.slides)}")
        print(f"   ✅ 转换状态: 成功")
    else:
        print("\n❌ 转换失败，请检查错误信息")
        print("💡 常见解决方案：")
        print("   1. 确保HTML文件使用UTF-8编码")
        print("   2. 检查文件是否被其他程序占用")
        print("   3. 确认有足够的磁盘空间")
        print("   4. 重新运行依赖安装脚本")

if __name__ == "__main__":
    main()
